'use client';

import { useState } from 'react';
import { ProgramIncrement } from '../types/jira';

interface PISelectorProps {
  programIncrements: ProgramIncrement[];
  selectedPI: ProgramIncrement | null;
  onPISelect: (pi: ProgramIncrement) => void;
}

export default function PISelector({ programIncrements, selectedPI, onPISelect }: PISelectorProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Избери Program Increment (PI)
      </label>
      
      <div className="relative">
        <button
          type="button"
          className="relative w-full bg-white border border-gray-300 rounded-md shadow-sm pl-3 pr-10 py-2 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          onClick={() => setIsOpen(!isOpen)}
        >
          <span className="block truncate">
            {selectedPI ? selectedPI.name : 'Избери PI...'}
          </span>
          <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <svg
              className="h-5 w-5 text-gray-400"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3z"
                clipRule="evenodd"
              />
            </svg>
          </span>
        </button>

        {isOpen && (
          <div className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
            {programIncrements.map((pi) => (
              <div
                key={pi.id}
                className="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-blue-50"
                onClick={() => {
                  onPISelect(pi);
                  setIsOpen(false);
                }}
              >
                <div className="flex flex-col">
                  <span className="font-medium text-gray-900">{pi.name}</span>
                  <span className="text-sm text-gray-500">
                    {new Date(pi.startDate).toLocaleDateString('bg-BG')} - {new Date(pi.endDate).toLocaleDateString('bg-BG')}
                  </span>
                  <span className="text-xs text-gray-400">
                    {pi.sprints.length} спринта
                  </span>
                </div>
                
                {selectedPI?.id === pi.id && (
                  <span className="absolute inset-y-0 right-0 flex items-center pr-4">
                    <svg
                      className="h-5 w-5 text-blue-600"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </span>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
