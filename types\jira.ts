// Jira User/Team Member
export interface JiraUser {
  accountId: string;
  displayName: string;
  emailAddress: string;
  avatarUrls: {
    '16x16': string;
    '24x24': string;
    '32x32': string;
    '48x48': string;
  };
  active: boolean;
}

// Jira Issue/Task
export interface JiraIssue {
  id: string;
  key: string;
  fields: {
    summary: string;
    description?: string;
    status: {
      name: string;
      statusCategory: {
        key: string;
        name: string;
      };
    };
    assignee?: <PERSON>ra<PERSON><PERSON>;
    reporter: <PERSON><PERSON><PERSON><PERSON>;
    priority: {
      name: string;
      iconUrl: string;
    };
    issuetype: {
      name: string;
      iconUrl: string;
    };
    storyPoints?: number;
    timeestimate?: number;
    timeoriginalestimate?: number;
    timespent?: number;
    created: string;
    updated: string;
    sprint?: JiraSprint;
  };
}

// Jira Sprint
export interface JiraSprint {
  id: number;
  name: string;
  state: 'FUTURE' | 'ACTIVE' | 'CLOSED';
  startDate?: string;
  endDate?: string;
  completeDate?: string;
  goal?: string;
  originBoardId: number;
}

// Program Increment (PI)
export interface ProgramIncrement {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  sprints: JiraSprint[];
  objectives?: string[];
}

// Team Capacity for a Sprint
export interface TeamCapacity {
  sprintId: number;
  sprintName: string;
  members: TeamMemberCapacity[];
  totalCapacity: number;
  totalCommitted: number;
  totalCompleted: number;
}

// Individual Team Member Capacity
export interface TeamMemberCapacity {
  user: JiraUser;
  capacity: number; // in hours or story points
  committed: number;
  completed: number;
  availability: number; // percentage (0-100)
}

// Board Data Structure
export interface JiraBoardData {
  pi: ProgramIncrement;
  sprints: SprintData[];
  teamMembers: JiraUser[];
}

// Sprint with Issues and Capacity
export interface SprintData {
  sprint: JiraSprint;
  issues: JiraIssue[];
  capacity: TeamCapacity;
  burndown?: BurndownData;
}

// Burndown Chart Data
export interface BurndownData {
  dates: string[];
  ideal: number[];
  actual: number[];
  remaining: number[];
}

// API Response Types
export interface JiraApiResponse<T> {
  expand?: string;
  startAt: number;
  maxResults: number;
  total: number;
  issues?: T[];
  values?: T[];
}

// Search Parameters
export interface SprintSearchParams {
  boardId?: number;
  state?: 'FUTURE' | 'ACTIVE' | 'CLOSED';
  startAt?: number;
  maxResults?: number;
}

export interface IssueSearchParams {
  jql: string;
  startAt?: number;
  maxResults?: number;
  fields?: string[];
  expand?: string[];
}
