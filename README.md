# Jira Board Dashboard

Уеб приложение за визуализация на Jira данни - спринтове, задачи и капацитет на екипа за определен Program Increment (PI).

## Функционалности

- 📊 **Преглед на спринтове** - Визуализация на всички спринтове в PI с прогрес и статус
- 👥 **Капацитет на екипа** - Проследяване на капацитета и натовареността на всеки член от екипа
- 📈 **Метрики и статистики** - Детайлни данни за завършени/планирани задачи
- 🎯 **PI фокус** - Филтриране и преглед по Program Increment
- 🔄 **Реално време** - Актуални данни от вашата Jira инсталация

## Настройка

### 1. Клониране на проекта

```bash
git clone <repository-url>
cd jirabord
```

### 2. Инсталиране на зависимости

```bash
npm install
```

### 3. Конфигуриране на Jira връзката

Създайте файл `.env.local` в root директорията:

```env
# Jira Configuration
JIRA_BASE_URL=http://localhost:8080
JIRA_USERNAME=your-username
JIRA_PASSWORD=your-password
# или използвайте API token вместо парола
JIRA_API_TOKEN=your-api-token

# Project Configuration
JIRA_PROJECT_KEY=YOUR_PROJECT_KEY
```

### 4. Стартиране на приложението

```bash
npm run dev
```

Отворете [http://localhost:3000](http://localhost:3000) в браузъра.

## Използване

1. **Конфигурация**: Въведете Board ID, Project Key и PI Name в формата за конфигурация
2. **Зареждане на данни**: Натиснете "Зареди данни" за да извлечете информацията от Jira
3. **Преглед**: Разгледайте спринтовете, задачите и капацитета на екипа

## Структура на проекта

```
jirabord/
├── app/                    # Next.js App Router
│   ├── api/jira/          # API routes за Jira интеграция
│   ├── page.tsx           # Главна страница
│   └── layout.tsx         # Layout компонент
├── components/            # React компоненти
│   ├── PISelector.tsx     # Селектор за Program Increment
│   ├── SprintCard.tsx     # Карта за спринт
│   └── TeamCapacityView.tsx # Преглед на капацитета
├── lib/                   # Библиотеки и сервиси
│   ├── jira-client.ts     # Jira API клиент
│   └── jira-service.ts    # Jira бизнес логика
├── types/                 # TypeScript типове
│   └── jira.ts           # Jira типове и интерфейси
└── .env.local            # Конфигурация (не е в git)
```

## API Endpoints

- `GET /api/jira/board` - Извличане на данни за борда

## Технологии

- **Next.js 15** - React framework
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **Axios** - HTTP клиент за Jira API

## Troubleshooting

### Проблеми с връзката към Jira

1. Проверете дали Jira сървърът е достъпен
2. Уверете се, че потребителските данни са правилни
3. Проверете дали имате права за достъп до проекта и бордовете

### CORS грешки

Ако получавате CORS грешки, може да се наложи да конфигурирате Jira сървъра да позволява заявки от вашия домейн.

## Разработка

За да добавите нови функционалности:

1. Добавете нови типове в `types/jira.ts`
2. Разширете `jira-service.ts` с нови методи
3. Създайте нови компоненти в `components/`
4. Добавете API routes в `app/api/`

## Лиценз

MIT License
