'use client';

import { SprintData } from '../types/jira';

interface SprintCardProps {
  sprintData: SprintData;
}

export default function SprintCard({ sprintData }: SprintCardProps) {
  const { sprint, issues, capacity } = sprintData;
  
  const getSprintStatusColor = (state: string) => {
    switch (state) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'CLOSED':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'FUTURE':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (state: string) => {
    switch (state) {
      case 'ACTIVE':
        return 'Активен';
      case 'CLOSED':
        return 'Завършен';
      case 'FUTURE':
        return 'Предстоящ';
      default:
        return state;
    }
  };

  const completionPercentage = capacity.totalCommitted > 0 
    ? Math.round((capacity.totalCompleted / capacity.totalCommitted) * 100)
    : 0;

  const capacityUtilization = capacity.totalCapacity > 0
    ? Math.round((capacity.totalCommitted / capacity.totalCapacity) * 100)
    : 0;

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6 card-hover">
      {/* Sprint Header */}
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{sprint.name}</h3>
          {sprint.goal && (
            <p className="text-sm text-gray-600 mt-1">{sprint.goal}</p>
          )}
        </div>
        <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getSprintStatusColor(sprint.state)}`}>
          {getStatusText(sprint.state)}
        </span>
      </div>

      {/* Sprint Dates */}
      <div className="flex flex-col sm:flex-row sm:justify-between text-sm text-gray-500 mb-4">
        <div>
          <span className="font-medium">Начало:</span> {sprint.startDate ? new Date(sprint.startDate).toLocaleDateString('bg-BG') : 'N/A'}
        </div>
        <div>
          <span className="font-medium">Край:</span> {sprint.endDate ? new Date(sprint.endDate).toLocaleDateString('bg-BG') : 'N/A'}
        </div>
      </div>

      {/* Progress Bars */}
      <div className="space-y-3 mb-4">
        {/* Completion Progress */}
        <div>
          <div className="flex justify-between text-sm mb-1">
            <span className="text-gray-700">Прогрес на задачите</span>
            <span className="text-gray-900 font-medium">{completionPercentage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-green-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${completionPercentage}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>{capacity.totalCompleted} завършени</span>
            <span>{capacity.totalCommitted} общо</span>
          </div>
        </div>

        {/* Capacity Utilization */}
        <div>
          <div className="flex justify-between text-sm mb-1">
            <span className="text-gray-700">Използване на капацитет</span>
            <span className="text-gray-900 font-medium">{capacityUtilization}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                capacityUtilization > 100 ? 'bg-red-500' : 
                capacityUtilization > 80 ? 'bg-yellow-500' : 'bg-blue-500'
              }`}
              style={{ width: `${Math.min(capacityUtilization, 100)}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>{capacity.totalCommitted} планирани</span>
            <span>{capacity.totalCapacity} капацитет</span>
          </div>
        </div>
      </div>

      {/* Issues Summary */}
      <div className="border-t pt-4">
        <div className="flex justify-between items-center mb-2">
          <h4 className="text-sm font-medium text-gray-900">Задачи</h4>
          <span className="text-sm text-gray-500">{issues.length} общо</span>
        </div>
        
        <div className="grid grid-cols-3 gap-2 text-xs">
          <div className="text-center p-2 bg-blue-50 rounded">
            <div className="font-medium text-blue-800">
              {issues.filter(i => i.fields.status.statusCategory.key === 'new').length}
            </div>
            <div className="text-blue-600">Нови</div>
          </div>
          <div className="text-center p-2 bg-yellow-50 rounded">
            <div className="font-medium text-yellow-800">
              {issues.filter(i => i.fields.status.statusCategory.key === 'indeterminate').length}
            </div>
            <div className="text-yellow-600">В прогрес</div>
          </div>
          <div className="text-center p-2 bg-green-50 rounded">
            <div className="font-medium text-green-800">
              {issues.filter(i => i.fields.status.statusCategory.key === 'done').length}
            </div>
            <div className="text-green-600">Готови</div>
          </div>
        </div>
      </div>
    </div>
  );
}
