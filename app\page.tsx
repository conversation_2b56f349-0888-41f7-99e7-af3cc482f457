'use client';

import { useState, useEffect } from 'react';
import PISelector from '../components/PISelector';
import SprintCard from '../components/SprintCard';
import TeamCapacityView from '../components/TeamCapacityView';
import { JiraBoardData, ProgramIncrement, SprintData, TeamCapacity } from '../types/jira';

export default function Home() {
  const [boardData, setBoardData] = useState<JiraBoardData | null>(null);
  const [selectedPI, setSelectedPI] = useState<ProgramIncrement | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [config, setConfig] = useState({
    boardId: 1,
    projectKey: 'PROJ',
    piName: 'PI 2024.1'
  });

  // Load board data
  const loadBoardData = async () => {
    if (!config.boardId || !config.projectKey) return;

    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        boardId: config.boardId.toString(),
        projectKey: config.projectKey,
        piName: config.piName
      });

      const response = await fetch(`/api/jira/board?${params}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch data');
      }

      const data: JiraBoardData = await response.json();
      setBoardData(data);
      setSelectedPI(data.pi);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Грешка при зареждане на данните');
      console.error('Error loading board data:', err);
    } finally {
      setLoading(false);
    }
  };

  // Configuration form
  const ConfigForm = () => (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6 mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Конфигурация</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Board ID
          </label>
          <input
            type="number"
            value={config.boardId}
            onChange={(e) => setConfig(prev => ({ ...prev, boardId: parseInt(e.target.value) || 1 }))}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Project Key
          </label>
          <input
            type="text"
            value={config.projectKey}
            onChange={(e) => setConfig(prev => ({ ...prev, projectKey: e.target.value }))}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            PI Name
          </label>
          <input
            type="text"
            value={config.piName}
            onChange={(e) => setConfig(prev => ({ ...prev, piName: e.target.value }))}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>
      <button
        onClick={loadBoardData}
        disabled={loading}
        className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {loading ? 'Зареждане...' : 'Зареди данни'}
      </button>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Jira Board Dashboard</h1>
          <p className="text-gray-600">Преглед на спринтове, задачи и капацитет на екипа</p>
        </div>

        {/* Configuration */}
        <ConfigForm />

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div className="flex">
              <div className="text-red-800">
                <strong>Грешка:</strong> {error}
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">Зареждане на данни от Jira...</p>
          </div>
        )}

        {/* Main Content */}
        {boardData && !loading && (
          <>
            {/* PI Selector */}
            <PISelector
              programIncrements={[boardData.pi]}
              selectedPI={selectedPI}
              onPISelect={setSelectedPI}
            />

            {selectedPI && (
              <>
                {/* PI Overview */}
                <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6 mb-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">{selectedPI.name}</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{selectedPI.sprints.length}</div>
                      <div className="text-sm text-gray-600">Спринта</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{boardData.teamMembers.length}</div>
                      <div className="text-sm text-gray-600">Членове на екипа</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {boardData.sprints.reduce((sum, s) => sum + s.issues.length, 0)}
                      </div>
                      <div className="text-sm text-gray-600">Общо задачи</div>
                    </div>
                  </div>
                </div>

                {/* Sprint Cards */}
                <div className="mb-8">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Спринтове</h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                    {boardData.sprints.map((sprintData) => (
                      <SprintCard key={sprintData.sprint.id} sprintData={sprintData} />
                    ))}
                  </div>
                </div>

                {/* Team Capacity */}
                <TeamCapacityView capacities={boardData.sprints.map(s => s.capacity)} />
              </>
            )}
          </>
        )}

        {/* Empty State */}
        {!boardData && !loading && !error && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Няма заредени данни</h3>
            <p className="text-gray-600">Конфигурирайте настройките и натиснете "Зареди данни" за да започнете.</p>
          </div>
        )}
      </div>
    </div>
  );
}
