import { NextRequest, NextResponse } from 'next/server';
import { jiraService } from '../../../../lib/jira-service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const boardId = parseInt(searchParams.get('boardId') || '1');
    const projectKey = searchParams.get('projectKey') || 'PROJ';
    const piName = searchParams.get('piName') || 'PI 2024.1';

    if (!boardId || !projectKey) {
      return NextResponse.json(
        { error: 'boardId and projectKey are required' },
        { status: 400 }
      );
    }

    const boardData = await jiraService.getBoardData(boardId, projectKey, piName);
    
    return NextResponse.json(boardData);
  } catch (error) {
    console.error('Error fetching board data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch board data', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
