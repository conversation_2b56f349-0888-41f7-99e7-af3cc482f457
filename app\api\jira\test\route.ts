import { NextResponse } from 'next/server';
import { jiraClient } from '../../../../lib/jira-client';

export async function GET() {
  try {
    const result = await jiraClient.testConnection();
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
        user: result.user
      });
    } else {
      return NextResponse.json({
        success: false,
        message: result.message
      }, { status: 400 });
    }
  } catch (error) {
    console.error('Test connection error:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to test connection',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
