import { jiraClient } from './jira-client';
import {
  Jira<PERSON>ser,
  Jira<PERSON>ssue,
  JiraSprint,
  JiraApiResponse,
  SprintSearchParams,
  IssueSearchParams,
  TeamCapacity,
  TeamMemberCapacity,
  ProgramIncrement,
  SprintData,
  JiraBoardData
} from '../types/jira';

export class JiraService {
  
  // Get all users/team members from a project
  async getProjectUsers(projectKey: string): Promise<JiraUser[]> {
    try {
      const response = await jiraClient.get<JiraUser[]>(
        `/project/${projectKey}/role`
      );
      
      // Get users from all roles
      const allUsers: JiraUser[] = [];
      const roles = await jiraClient.get<any>(`/project/${projectKey}/role`);
      
      for (const roleUrl of Object.values(roles)) {
        const roleData = await jiraClient.get<any>(roleUrl as string);
        if (roleData.actors) {
          roleData.actors.forEach((actor: any) => {
            if (actor.type === 'atlassian-user-role-actor' && actor.actorUser) {
              allUsers.push(actor.actorUser);
            }
          });
        }
      }
      
      // Remove duplicates
      const uniqueUsers = allUsers.filter((user, index, self) => 
        index === self.findIndex(u => u.accountId === user.accountId)
      );
      
      return uniqueUsers;
    } catch (error) {
      console.error('Error fetching project users:', error);
      return [];
    }
  }

  // Get sprints from a board
  async getSprints(boardId: number, params?: SprintSearchParams): Promise<JiraSprint[]> {
    try {
      const response = await jiraClient.get<JiraApiResponse<JiraSprint>>(
        `/board/${boardId}/sprint`,
        params
      );
      return response.values || [];
    } catch (error) {
      console.error('Error fetching sprints:', error);
      return [];
    }
  }

  // Get issues from a sprint
  async getSprintIssues(sprintId: number): Promise<JiraIssue[]> {
    try {
      const response = await jiraClient.get<JiraApiResponse<JiraIssue>>(
        `/sprint/${sprintId}/issue`,
        {
          fields: [
            'summary',
            'description',
            'status',
            'assignee',
            'reporter',
            'priority',
            'issuetype',
            'customfield_10016', // Story Points
            'timeestimate',
            'timeoriginalestimate',
            'timespent',
            'created',
            'updated'
          ].join(',')
        }
      );
      return response.issues || [];
    } catch (error) {
      console.error('Error fetching sprint issues:', error);
      return [];
    }
  }

  // Search for issues using JQL
  async searchIssues(params: IssueSearchParams): Promise<JiraIssue[]> {
    try {
      const response = await jiraClient.get<JiraApiResponse<JiraIssue>>(
        '/search',
        params
      );
      return response.issues || [];
    } catch (error) {
      console.error('Error searching issues:', error);
      return [];
    }
  }

  // Get all boards
  async getBoards(): Promise<any[]> {
    try {
      const response = await jiraClient.get<JiraApiResponse<any>>('/board');
      return response.values || [];
    } catch (error) {
      console.error('Error fetching boards:', error);
      return [];
    }
  }

  // Calculate team capacity for a sprint
  async calculateTeamCapacity(
    sprintId: number, 
    teamMembers: JiraUser[]
  ): Promise<TeamCapacity> {
    const sprint = await this.getSprintById(sprintId);
    const issues = await this.getSprintIssues(sprintId);
    
    const memberCapacities: TeamMemberCapacity[] = teamMembers.map(member => {
      const memberIssues = issues.filter(issue => 
        issue.fields.assignee?.accountId === member.accountId
      );
      
      const committed = memberIssues.reduce((sum, issue) => 
        sum + (issue.fields.storyPoints || 0), 0
      );
      
      const completed = memberIssues
        .filter(issue => issue.fields.status.statusCategory.key === 'done')
        .reduce((sum, issue) => sum + (issue.fields.storyPoints || 0), 0);
      
      return {
        user: member,
        capacity: 40, // Default 40 hours per sprint (2 weeks * 20 hours)
        committed,
        completed,
        availability: 100 // Default 100% availability
      };
    });

    const totalCapacity = memberCapacities.reduce((sum, mc) => sum + mc.capacity, 0);
    const totalCommitted = memberCapacities.reduce((sum, mc) => sum + mc.committed, 0);
    const totalCompleted = memberCapacities.reduce((sum, mc) => sum + mc.completed, 0);

    return {
      sprintId,
      sprintName: sprint?.name || `Sprint ${sprintId}`,
      members: memberCapacities,
      totalCapacity,
      totalCommitted,
      totalCompleted
    };
  }

  // Get sprint by ID
  async getSprintById(sprintId: number): Promise<JiraSprint | null> {
    try {
      const response = await jiraClient.get<JiraSprint>(`/sprint/${sprintId}`);
      return response;
    } catch (error) {
      console.error('Error fetching sprint:', error);
      return null;
    }
  }

  // Create a Program Increment from sprints
  createProgramIncrement(
    name: string,
    sprints: JiraSprint[],
    startDate?: string,
    endDate?: string
  ): ProgramIncrement {
    const sortedSprints = sprints.sort((a, b) => 
      new Date(a.startDate || '').getTime() - new Date(b.startDate || '').getTime()
    );

    const piStartDate = startDate || sortedSprints[0]?.startDate || new Date().toISOString();
    const piEndDate = endDate || sortedSprints[sortedSprints.length - 1]?.endDate || new Date().toISOString();

    return {
      id: `PI-${Date.now()}`,
      name,
      startDate: piStartDate,
      endDate: piEndDate,
      sprints: sortedSprints
    };
  }

  // Get complete board data for a PI
  async getBoardData(
    boardId: number,
    projectKey: string,
    piName: string,
    sprintStates: ('FUTURE' | 'ACTIVE' | 'CLOSED')[] = ['ACTIVE', 'CLOSED']
  ): Promise<JiraBoardData> {
    try {
      // Get team members
      const teamMembers = await this.getProjectUsers(projectKey);
      
      // Get sprints
      const allSprints: JiraSprint[] = [];
      for (const state of sprintStates) {
        const sprints = await this.getSprints(boardId, { state, maxResults: 50 });
        allSprints.push(...sprints);
      }
      
      // Create PI
      const pi = this.createProgramIncrement(piName, allSprints);
      
      // Get sprint data with issues and capacity
      const sprintDataPromises = allSprints.map(async (sprint): Promise<SprintData> => {
        const [issues, capacity] = await Promise.all([
          this.getSprintIssues(sprint.id),
          this.calculateTeamCapacity(sprint.id, teamMembers)
        ]);
        
        return {
          sprint,
          issues,
          capacity
        };
      });
      
      const sprints = await Promise.all(sprintDataPromises);
      
      return {
        pi,
        sprints,
        teamMembers
      };
    } catch (error) {
      console.error('Error fetching board data:', error);
      throw error;
    }
  }
}

export const jiraService = new JiraService();
