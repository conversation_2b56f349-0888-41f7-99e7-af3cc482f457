import axios, { AxiosInstance } from 'axios';

export class JiraClient {
  private client: AxiosInstance;

  constructor() {
    const baseURL = process.env.JIRA_BASE_URL || 'http://localhost:8080';
    const username = process.env.JIRA_USERNAME;
    const password = process.env.JIRA_PASSWORD;
    const apiToken = process.env.JIRA_API_TOKEN;

    this.client = axios.create({
      baseURL: `${baseURL}/rest/api/2`,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Setup authentication
    if (apiToken && username) {
      // Use API token authentication
      this.client.defaults.auth = {
        username: username,
        password: apiToken,
      };
    } else if (username && password) {
      // Use basic authentication
      this.client.defaults.auth = {
        username: username,
        password: password,
      };
    }

    // Add request interceptor for debugging
    this.client.interceptors.request.use(
      (config) => {
        console.log(`Making request to: ${config.url}`);
        return config;
      },
      (error) => {
        console.error('Request error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('Response error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  async get<T>(endpoint: string, params?: any): Promise<T> {
    const response = await this.client.get<T>(endpoint, { params });
    return response.data;
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    const response = await this.client.post<T>(endpoint, data);
    return response.data;
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    const response = await this.client.put<T>(endpoint, data);
    return response.data;
  }

  async delete<T>(endpoint: string): Promise<T> {
    const response = await this.client.delete<T>(endpoint);
    return response.data;
  }
}

// Singleton instance
export const jiraClient = new JiraClient();
