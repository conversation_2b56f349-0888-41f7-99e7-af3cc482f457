import axios, { AxiosInstance } from 'axios';
import https from 'https';

export class JiraClient {
  private client: AxiosInstance;

  constructor() {
    const baseURL = process.env.JIRA_BASE_URL || 'http://localhost:8080';
    const username = process.env.JIRA_USERNAME;
    const password = process.env.JIRA_PASSWORD;
    const apiToken = process.env.JIRA_API_TOKEN;

    // Create HTTPS agent with configurable SSL verification
    const rejectUnauthorized = process.env.JIRA_REJECT_UNAUTHORIZED !== 'false';
    const httpsAgent = new https.Agent({
      rejectUnauthorized: rejectUnauthorized
    });

    this.client = axios.create({
      baseURL: `${baseURL}/rest/api/2`,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      httpsAgent: httpsAgent,
      timeout: 30000, // 30 second timeout
    });

    // Setup authentication
    if (apiToken && username) {
      // Use API token authentication (for Jira Cloud/Server with API tokens)
      const token = Buffer.from(`${username}:${apiToken}`).toString('base64');
      this.client.defaults.headers.common['Authorization'] = `Basic ${token}`;
    } else if (username && password) {
      // Use basic authentication
      const token = Buffer.from(`${username}:${password}`).toString('base64');
      this.client.defaults.headers.common['Authorization'] = `Basic ${token}`;
    }

    // Add request interceptor for debugging
    this.client.interceptors.request.use(
      (config) => {
        console.log(`Making request to: ${config.url}`);
        return config;
      },
      (error) => {
        console.error('Request error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.code === 'SELF_SIGNED_CERT_IN_CHAIN') {
          console.error('SSL Certificate error. Try setting JIRA_REJECT_UNAUTHORIZED=false in .env.local');
        } else if (error.response?.status === 401) {
          console.error('Authentication failed. Check your username/password or API token.');
        } else if (error.response?.status === 403) {
          console.error('Access forbidden. Check your permissions for this project/board.');
        } else if (error.response?.status === 404) {
          console.error('Resource not found. Check your board ID or project key.');
        } else {
          console.error('Response error:', error.response?.data || error.message);
        }
        return Promise.reject(error);
      }
    );
  }

  async get<T>(endpoint: string, params?: any): Promise<T> {
    const response = await this.client.get<T>(endpoint, { params });
    return response.data;
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    const response = await this.client.post<T>(endpoint, data);
    return response.data;
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    const response = await this.client.put<T>(endpoint, data);
    return response.data;
  }

  async delete<T>(endpoint: string): Promise<T> {
    const response = await this.client.delete<T>(endpoint);
    return response.data;
  }

  // Test connection to Jira
  async testConnection(): Promise<{ success: boolean; message: string; user?: any }> {
    try {
      const response = await this.get('/myself');
      return {
        success: true,
        message: 'Connection successful',
        user: response
      };
    } catch (error: any) {
      let message = 'Connection failed';

      if (error.code === 'SELF_SIGNED_CERT_IN_CHAIN') {
        message = 'SSL Certificate error. Set JIRA_REJECT_UNAUTHORIZED=false in .env.local';
      } else if (error.response?.status === 401) {
        message = 'Authentication failed. Check credentials.';
      } else if (error.response?.status === 403) {
        message = 'Access forbidden. Check permissions.';
      } else if (error.code === 'ECONNREFUSED') {
        message = 'Connection refused. Check if Jira server is running.';
      } else {
        message = error.message || 'Unknown error';
      }

      return {
        success: false,
        message
      };
    }
  }
}

// Singleton instance
export const jiraClient = new JiraClient();
