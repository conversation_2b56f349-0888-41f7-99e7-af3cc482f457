import axios, { AxiosInstance } from 'axios';
import https from 'https';

export class JiraClient {
  private client: AxiosInstance;

  constructor() {
    const baseURL = process.env.JIRA_BASE_URL || 'http://localhost:8080';
    const username = process.env.JIRA_USERNAME;
    const password = process.env.JIRA_PASSWORD;
    const apiToken = process.env.JIRA_API_TOKEN;

    // Create HTTPS agent with configurable SSL verification
    const rejectUnauthorized = process.env.JIRA_REJECT_UNAUTHORIZED !== 'false';
    const httpsAgent = new https.Agent({
      rejectUnauthorized: rejectUnauthorized
    });

    // Clean up baseURL - remove trailing slash if present
    const cleanBaseURL = baseURL.endsWith('/') ? baseURL.slice(0, -1) : baseURL;

    this.client = axios.create({
      baseURL: `${cleanBaseURL}/rest/api/2`,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      httpsAgent: httpsAgent,
      timeout: 30000, // 30 second timeout
    });

    console.log('Final base URL:', `${cleanBaseURL}/rest/api/2`);

    // Setup authentication
    console.log('Setting up authentication...');
    console.log('Username:', username);
    console.log('Has password:', !!password);
    console.log('Has API token:', !!apiToken);

    if (apiToken && username) {
      // Use API token authentication (for Jira Cloud/Server with API tokens)
      console.log('Using API token authentication');
      this.client.defaults.auth = {
        username: username,
        password: apiToken
      };
    } else if (username && password) {
      // Use basic authentication
      console.log('Using password authentication');
      this.client.defaults.auth = {
        username: username,
        password: password
      };
    } else {
      console.log('No authentication configured!');
    }

    // Add request interceptor for debugging
    this.client.interceptors.request.use(
      (config) => {
        console.log(`Making request to: ${config.url}`);
        return config;
      },
      (error) => {
        console.error('Request error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.code === 'SELF_SIGNED_CERT_IN_CHAIN') {
          console.error('SSL Certificate error. Try setting JIRA_REJECT_UNAUTHORIZED=false in .env.local');
        } else if (error.response?.status === 401) {
          console.error('Authentication failed. Check your username/password or API token.');
        } else if (error.response?.status === 403) {
          console.error('Access forbidden. Check your permissions for this project/board.');
        } else if (error.response?.status === 404) {
          console.error('Resource not found. Check your board ID or project key.');
        } else {
          console.error('Response error:', error.response?.data || error.message);
        }
        return Promise.reject(error);
      }
    );
  }

  async get<T>(endpoint: string, params?: any): Promise<T> {
    const response = await this.client.get<T>(endpoint, { params });
    return response.data;
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    const response = await this.client.post<T>(endpoint, data);
    return response.data;
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    const response = await this.client.put<T>(endpoint, data);
    return response.data;
  }

  async delete<T>(endpoint: string): Promise<T> {
    const response = await this.client.delete<T>(endpoint);
    return response.data;
  }

  // Test connection to Jira
  async testConnection(): Promise<{ success: boolean; message: string; user?: any }> {
    try {
      console.log('Testing connection to Jira...');
      console.log('Base URL:', this.client.defaults.baseURL);
      console.log('Auth header:', this.client.defaults.headers.common['Authorization'] ? 'Set' : 'Not set');

      // Try different endpoints that might work
      let response;
      try {
        response = await this.get('/myself');
      } catch (myselfError) {
        console.log('Failed with /myself, trying /serverInfo');
        try {
          response = await this.get('/serverInfo');
          return {
            success: true,
            message: 'Connection successful (via serverInfo)',
            user: response
          };
        } catch (serverInfoError) {
          console.log('Failed with /serverInfo, trying basic auth test');
          // Try a simple request to see if we can connect at all
          const testResponse = await this.client.get('/serverInfo');
          return {
            success: true,
            message: 'Connection successful (basic)',
            user: testResponse.data
          };
        }
      }

      return {
        success: true,
        message: 'Connection successful',
        user: response
      };
    } catch (error: any) {
      console.log('Full error:', error);
      console.log('Error response:', error.response?.data);
      console.log('Error status:', error.response?.status);

      let message = 'Connection failed';

      if (error.code === 'SELF_SIGNED_CERT_IN_CHAIN') {
        message = 'SSL Certificate error. Set JIRA_REJECT_UNAUTHORIZED=false in .env.local';
      } else if (error.response?.status === 401) {
        message = `Authentication failed. Status: ${error.response.status}. Response: ${JSON.stringify(error.response.data)}`;
      } else if (error.response?.status === 403) {
        message = 'Access forbidden. Check permissions.';
      } else if (error.code === 'ECONNREFUSED') {
        message = 'Connection refused. Check if Jira server is running.';
      } else if (error.code === 'ENOTFOUND') {
        message = 'Host not found. Check the JIRA_BASE_URL.';
      } else {
        message = `${error.message || 'Unknown error'}. Code: ${error.code}`;
      }

      return {
        success: false,
        message
      };
    }
  }
}

// Singleton instance
export const jiraClient = new JiraClient();
