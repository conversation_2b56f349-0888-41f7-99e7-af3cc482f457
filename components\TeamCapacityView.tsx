'use client';

import { TeamCapacity } from '../types/jira';

interface TeamCapacityViewProps {
  capacities: TeamCapacity[];
}

export default function TeamCapacityView({ capacities }: TeamCapacityViewProps) {
  if (capacities.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Капацитет на екипа</h3>
        <p className="text-gray-500">Няма данни за капацитет</p>
      </div>
    );
  }

  // Get all unique team members across all sprints
  const allMembers = Array.from(
    new Set(
      capacities.flatMap(c => c.members.map(m => m.user.accountId))
    )
  ).map(accountId => {
    const member = capacities
      .flatMap(c => c.members)
      .find(m => m.user.accountId === accountId);
    return member?.user;
  }).filter(Boolean);

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Капацитет на екипа по спринтове</h3>
      
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-blue-800">
            {capacities.reduce((sum, c) => sum + c.totalCapacity, 0)}
          </div>
          <div className="text-blue-600 text-sm">Общ капацитет</div>
        </div>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-yellow-800">
            {capacities.reduce((sum, c) => sum + c.totalCommitted, 0)}
          </div>
          <div className="text-yellow-600 text-sm">Планирани точки</div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-green-800">
            {capacities.reduce((sum, c) => sum + c.totalCompleted, 0)}
          </div>
          <div className="text-green-600 text-sm">Завършени точки</div>
        </div>
      </div>

      {/* Capacity Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Спринт
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Капацитет
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Планирани
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Завършени
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Използване
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Прогрес
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {capacities.map((capacity) => {
              const utilization = capacity.totalCapacity > 0 
                ? Math.round((capacity.totalCommitted / capacity.totalCapacity) * 100)
                : 0;
              const completion = capacity.totalCommitted > 0
                ? Math.round((capacity.totalCompleted / capacity.totalCommitted) * 100)
                : 0;

              return (
                <tr key={capacity.sprintId} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {capacity.sprintName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {capacity.totalCapacity}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {capacity.totalCommitted}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {capacity.totalCompleted}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-1 mr-2">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${
                              utilization > 100 ? 'bg-red-500' : 
                              utilization > 80 ? 'bg-yellow-500' : 'bg-blue-500'
                            }`}
                            style={{ width: `${Math.min(utilization, 100)}%` }}
                          ></div>
                        </div>
                      </div>
                      <span className="text-sm text-gray-600">{utilization}%</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-1 mr-2">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-green-500 h-2 rounded-full"
                            style={{ width: `${completion}%` }}
                          ></div>
                        </div>
                      </div>
                      <span className="text-sm text-gray-600">{completion}%</span>
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Team Members Section */}
      <div className="mt-8">
        <h4 className="text-md font-semibold text-gray-900 mb-4">Членове на екипа</h4>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {allMembers.map((member) => {
            if (!member) return null;
            
            const memberCapacities = capacities.map(c => 
              c.members.find(m => m.user.accountId === member.accountId)
            ).filter(Boolean);

            const totalCommitted = memberCapacities.reduce((sum, mc) => sum + (mc?.committed || 0), 0);
            const totalCompleted = memberCapacities.reduce((sum, mc) => sum + (mc?.completed || 0), 0);

            return (
              <div key={member.accountId} className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center mb-2">
                  <img 
                    src={member.avatarUrls['32x32']} 
                    alt={member.displayName}
                    className="w-8 h-8 rounded-full mr-3"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">{member.displayName}</div>
                    <div className="text-xs text-gray-500">{member.emailAddress}</div>
                  </div>
                </div>
                <div className="text-xs text-gray-600">
                  <div>Планирани: {totalCommitted}</div>
                  <div>Завършени: {totalCompleted}</div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
